<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号码" clearable />
      </el-form-item>
      <el-form-item label="场景" prop="scene">
        <el-select v-model="queryParams.scene" placeholder="请选择场景" clearable>
          <el-option v-for="item in sceneOptions" :key="item.sceneCode" :label="item.sceneName"
            :value="item.sceneCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="服饰" prop="costume_id">
        <el-select v-model="queryParams.costume_id" placeholder="请选择服饰" clearable>
          <el-option v-for="item in costumeOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="未支付" value="0" />
          <el-option label="已支付" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="createTimes" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" style="width: 250px" @change="handleCreateTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="下单时间">
        <el-date-picker v-model="payTimes" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" style="width: 250px" @change="handleOrderTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="2">
        <el-button type="primary" plain icon="el-icon-download" size="mini" @click="handleExport">批量导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column label="序号" align="center" type="index" width="60"></el-table-column>
      <el-table-column prop="userId" label="用户编号" align="center"></el-table-column>
      <el-table-column prop="userName" label="用户名称" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
      <el-table-column prop="sceneName" label="场景" align="center"></el-table-column>
      <el-table-column prop="costumeName" label="服饰" align="center"></el-table-column>
      <el-table-column prop="statusName" label="状态" align="center"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="actualAmount" label="实付金额" align="center"></el-table-column>
      <el-table-column prop="orderTime" label="下单时间" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template #default="{row}">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <detail-dialog ref="detailDialog" />
  </div>
</template>

<script>
import { getSceneOptions, getCostumeOptions, getAiFaceChangeOrderList } from "@/api/aiFaceChange";
import DetailDialog from "./components/detailDialog.vue";

export default {
  name: "AiFaceChangeOrderList",
  components: {
    DetailDialog
  },
  data() {
    return {
      showSearch: true,
      queryParams: {
        phone: '', // 手机号
        scene: '',
        costume_id: '', // 服饰id
        status: '',
        createTimeStart: '',
        createTimeEnd: '',
        payTimeStart: '',
        payTimeEnd: '',
        pageNum: 1,
        pageSize: 10,
      },
      sceneOptions: [],
      costumeOptions: [],
      tableData: [
        {
          id: 1,
          userId: '19420606779018936341',
          userName: '微信用户',
          phone: '18641985526',
          sceneName: '大唐不夜',
          costumeName: '步摇惊棠',
          statusName: '已支付',
          createTime: '2025-07-07 09:56:48',
          actualAmount: '2.00',
          orderTime: '2025-07-07 09:56:48'
        },
        {
          id: 2,
          userId: '19420606779018936342',
          userName: '张三',
          phone: '13812345678',
          sceneName: '汉服体验',
          costumeName: '襦裙套装',
          statusName: '未支付',
          createTime: '2025-07-06 14:30:20',
          actualAmount: '5.00',
          orderTime: '2025-07-06 14:30:20'
        },
        {
          id: 3,
          userId: '19420606779018936343',
          userName: '李四',
          phone: '15987654321',
          sceneName: '古风摄影',
          costumeName: '凤冠霞帔',
          statusName: '已支付',
          createTime: '2025-07-05 16:45:12',
          actualAmount: '8.00',
          orderTime: '2025-07-05 16:45:12'
        },
        {
          id: 4,
          userId: '19420606779018936344',
          userName: '王五',
          phone: '13698745632',
          sceneName: '宫廷风',
          costumeName: '龙袍',
          statusName: '未支付',
          createTime: '2025-07-04 10:20:35',
          actualAmount: '12.00',
          orderTime: '2025-07-04 10:20:35'
        },
        {
          id: 5,
          userId: '19420606779018936345',
          userName: '赵六',
          phone: '18756432109',
          sceneName: '民国风',
          costumeName: '旗袍',
          statusName: '已支付',
          createTime: '2025-07-03 11:15:28',
          actualAmount: '6.50',
          orderTime: '2025-07-03 11:15:28'
        }
      ],
      total: 5,
      loading: false,
      createTimes: [],
      payTimes: []
    }
  },
  mounted() {
    // 注释掉API调用，使用假数据
    // this.getList()
    this.getOptions()
  },
  methods: {
    async getOptions() {
      const { data } = await getSceneOptions()
      const { data: costumeOptions } = await getCostumeOptions()
      this.sceneOptions = data
      this.costumeOptions = costumeOptions
    },
    async getList() {
      this.loading = true
      try {
        // 模拟API调用延迟
        // await new Promise(resolve => setTimeout(resolve, 500))
        // 使用假数据，实际项目中取消注释下面的代码
        const { rows, total } = await getAiFaceChangeOrderList(this.queryParams)
        this.tableData = rows
        this.total = total
      } finally {
        this.loading = false
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.createTimes = []
      this.orderTimes = []
      this.queryParams.createTimeStart = ''
      this.queryParams.createTimeEnd = ''
      this.queryParams.orderTimeStart = ''
      this.queryParams.orderTimeEnd = ''
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleCreateTimeChange(times) {
      if (times && times.length === 2) {
        this.queryParams.createTimeStart = times[0] + ' 00:00:00'
        this.queryParams.createTimeEnd = times[1] + ' 23:59:59'
      } else {
        this.queryParams.createTimeStart = ''
        this.queryParams.createTimeEnd = ''
      }
    },
    handleOrderTimeChange(times) {
      const [start, end] = times || [];
      this.queryParams.orderTimeStart = start ? `${start} 00:00:00` : '';
      this.queryParams.orderTimeEnd = end ? `${end} 23:59:59` : '';
    },
    handleExport() {
      this.download('/ai-face-change/order/export', {
        ...this.queryParams
      }, `AI换脸订单信息_${new Date().getTime()}.xlsx`)
    },
    handleDetail(row) {
      // 打开详情弹窗
      this.$refs.detailDialog.open(row)
    }
  }
}
</script>

<style scoped lang="scss">
</style>

