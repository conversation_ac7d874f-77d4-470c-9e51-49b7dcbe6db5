<template>
  <common-dialog title="详情" :visible.sync="visible" width="500px" :is-view="true">
    <div class="detail-container">


      <!-- 用户信息区域 -->
      <div class="info-section">
        <div class="section-header">
          <div class="section-title">用户信息</div>
          <!-- 状态标签 -->
          <div class="status-tag">
            已支付
          </div>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">用户编号：</span>
            <span class="value">{{ details.userId }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户名称：</span>
            <span class="value">{{ details.userName }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号码：</span>
            <span class="value">{{ details.phone }}</span>
          </div>
        </div>
      </div>

      <!-- 场景信息区域 -->
      <div class="info-section">
        <div class="section-header">
          <div class="section-title">场景信息</div>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">场景：</span>
            <span class="value">{{ details.sceneName }}</span>
          </div>
          <div class="info-item">
            <span class="label">服饰：</span>
            <span class="value">{{ details.costumeName }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ details.createTime }}</span>
          </div>
        </div>
      </div>

      <!-- 订单信息区域 -->
      <div class="info-section">
        <div class="section-header">
          <div class="section-title">订单信息</div>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">实付金额：</span>
            <span class="value amount">{{ details.actualAmount }}</span>
          </div>
          <div class="info-item">
            <span class="label">下单时间：</span>
            <span class="value">{{ details.orderTime }}</span>
          </div>
        </div>
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { getAiFaceChangeOrderDetail } from "@/api/aiFaceChange";

export default {
  name: "detailDialog",
  data() {
    return {
      visible: false,
      details: {
        userId: '',
        userName: '',
        phone: '',
        sceneName: '',
        costumeName: '',
        createTime: '',
        actualAmount: '',
        orderTime: ''
      }
    }
  },
  methods: {
    async open(row) {
      this.visible = true
      if (row && row.id) {
        try {
          const { data } = await getAiFaceChangeOrderDetail({ id: row.id })
          this.details = data
        } catch (error) {
          console.error('获取详情失败:', error)
        }
      } else {
        // 如果没有传入row.id，直接使用传入的row数据
        this.details = { ...row }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.detail-container {
  position: relative;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.status-tag {
  background: #67C23A;
  color: white;
  padding: 6px 10px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-section {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 12px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 6px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  padding: 8px 0 8px 12px;
  border-left: 3px solid #409EFF;
  position: relative;
}

.info-content {
  padding: 0 4px;
  background: #fafafa;
  border-radius: 4px;
  padding: 12px 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  min-height: 20px;
  line-height: 1.4;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-size: 14px;
    color: #606266;
    min-width: 85px;
    flex-shrink: 0;
    font-weight: 400;
    padding-top: 1px;
  }

  .value {
    font-size: 14px;
    color: #303133;
    flex: 1;
    word-break: break-all;
    padding-top: 1px;

    &.amount {
      color: #E6A23C;
      font-weight: 600;
    }
  }
}

// 覆盖弹窗样式
::v-deep .el-dialog__body {
  padding: 20px !important;
}
</style>