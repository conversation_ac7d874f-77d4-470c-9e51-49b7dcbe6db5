import request from '@/utils/request'

// 获取场景列表
export function getSceneOptions() {
  return request({
    url: '/museum-app/ai-costume/getSceneList',
    method: 'get'
  })
}
// 获取服饰列表 ai-costume/getCostumeList
export function getCostumeOptions() {
  return request({
    url: '/museum-app/ai-costume/getCostumeList',
    method: 'get'
  })
}

// AI换脸订单列表
export function getAiFaceChangeOrderList(params) {
  return request({
    url: '/museum-app/ai-face/getListByCondition',
    method: 'get',
    params
  })
}
/* --------------------------------------华丽的分割线------------------------------------ */

// AI换脸订单详情
export function getAiFaceChangeOrderDetail(params) {
  return request({
    url: '/ai-face-change/order/detail',
    method: 'get',
    params
  })
}

// AI换脸价格配置列表
export function getAiFaceChangePriceList(params) {
  return request({
    url: '/ai-face-change/price/list',
    method: 'get',
    params
  })
}

// AI换脸价格配置详情
export function getAiFaceChangePriceDetail(params) {
  return request({
    url: '/ai-face-change/price/detail',
    method: 'get',
    params
  })
}

// 新增AI换脸价格配置
export function addAiFaceChangePrice(data) {
  return request({
    url: '/ai-face-change/price/add',
    method: 'post',
    data
  })
}

// 更新AI换脸价格配置
export function updateAiFaceChangePrice(data) {
  return request({
    url: '/ai-face-change/price/update',
    method: 'put',
    data
  })
}

// 失效AI换脸价格配置
export function disableAiFaceChangePrice(data) {
  return request({
    url: '/ai-face-change/price/disable',
    method: 'put',
    data
  })
}